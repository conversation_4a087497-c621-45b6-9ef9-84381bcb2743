FROM golang:1.23.4 AS builder
ENV GO111MODULE=on

WORKDIR /jc-solana-indexer
COPY . .

RUN go mod tidy
RUN go build -o ./bin/jc-sipanzi ./cmd/solana-indexer

FROM ubuntu:latest

COPY --from=builder /usr/local/go /usr/local/go
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV PATH="${GOPATH}/bin:${PATH}"

ENV ENV="local"

RUN apt update && apt install -y libpq5 ca-certificates curl

WORKDIR /app

COPY --from=builder /jc-solana-indexer/bin/solana-indexer .

COPY --from=builder /jc-solana-indexer/config ./config

RUN chmod a+x ./sipanzi

EXPOSE 18080

ENTRYPOINT ["sh","-c","./solana-indexer -conf config/config-$ENV.yaml"]
