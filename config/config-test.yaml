db:
  host: "cex-mysql-cedefi-test.cluster-c5mgk4qm8m2z.ap-southeast-1.rds.amazonaws.com"
  port: 3358
  user: "cedefi_rw"
  password: "Nf1GqxhbZ4p4bXSR"
  dbname: "cedefi_indexer"

rdb:
  host: "clustercfg.cex-redis-cedefi-test.exwdne.apse1.cache.amazonaws.com"
  port: 6379
  password: "xmBIH6iQDgfbVeYg"
  db: 0
  insecure_skip_verify: true

kafka:
  brokers:
    - "b-1.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
    - "b-2.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
    - "b-3.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
  user: ""
  password: ""

server:
  health:
    port: 8031
  grpc:
    port: 9000

solana:
  # solana rpc URL
  rpc_url: "https://api.chainup.net/solana/mainnet/6e3198189f654b7fa468606e8dc791a4"

  # solana websocket URL
  ws_url: "wss://mainnet.helius-rpc.com/?api-key=1a48a418-f65c-467c-a59a-8d1fea820675"

  # pumpfun 解析开关
  pumpfun_parser_enabled: true

  # pumpswap 解析开关
  pumpswap_parser_enabled: true

  # raydium 解析开关
  raydium_parser_enabled: true

  # raydium cpmm 解析开关
  raydium_cpmm_parser_enabled: true

  # raydium clmm 解析开关
  raydium_clmm_parser_enabled: true

  # 向前爬块开关
  watch_forward: true

  # 向前爬块并发数
  forward_routine_nums: 20

solscan:
  # solscan api uri
  api_url: "https://pro-api.solscan.io/v2.0"

  # solscan api key
  api_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.YwdpXmo_ly4LxumIaL0ke5axMCQ-xu3VGzBNxBzOBDA"