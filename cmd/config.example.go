package main

// 配置示例文件
// 复制此文件为 config.go 并填入您的真实配置信息

const (
	// 飞书应用配置
	// 从飞书开放平台获取：https://open.feishu.cn/
	ExampleAppID     = "cli_a1b2c3d4e5f6g7h8"  // 您的应用ID
	ExampleAppSecret = "your_app_secret_here"    // 您的应用密钥
	
	// 可选：指定文件夹Token，留空则创建在根目录
	ExampleFolderToken = ""
	
	// 表格配置
	ExampleSpreadsheetTitle = "库存管理表格"
)

// 示例数据结构
type InventoryItem struct {
	Name     string  // 商品名称
	Quantity int     // 数量
	Price    float64 // 价格（可选）
	Time     string  // 时间
}

// 示例数据
var ExampleInventoryData = []InventoryItem{
	{Name: "苹果", Quantity: 100, Price: 5.5, Time: "2025-01-05 10:00:00"},
	{Name: "香蕉", Quantity: 50, Price: 3.2, Time: "2025-01-05 09:00:00"},
	{Name: "橙子", Quantity: 75, Price: 4.8, Time: "2025-01-05 08:00:00"},
	{Name: "葡萄", Quantity: 30, Price: 12.0, Time: "2025-01-05 07:00:00"},
	{Name: "西瓜", Quantity: 20, Price: 15.5, Time: "2025-01-05 06:00:00"},
}

// 权限说明
/*
需要在飞书开放平台为您的应用申请以下权限：

必需权限：
- sheets:spreadsheet:create     创建电子表格
- sheets:spreadsheet           查看、编辑和管理电子表格  
- drive:drive                  查看、编辑、创建和删除云空间中的文件

可选权限（根据需要）：
- sheets:spreadsheet:read_only  只读访问电子表格
- drive:drive:readonly         只读访问云空间文件

权限申请步骤：
1. 登录飞书开放平台 https://open.feishu.cn/
2. 进入您的应用管理页面
3. 点击"权限管理"
4. 搜索并添加上述权限
5. 提交审核（企业自建应用通常自动通过）
*/
