# 飞书电子表格创建工具

这个程序使用飞书 Lark SDK 创建一个包含名称、数量、时间等字段的电子表格。

## 功能特性

- 使用飞书开放平台 API 创建电子表格
- 自动添加表头（名称、数量、时间）
- 插入示例数据
- 支持批量数据更新
- 完整的错误处理

## 使用前准备

### 1. 创建飞书应用

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 登录并创建企业自建应用
3. 获取应用的 `App ID` 和 `App Secret`
4. 在应用权限管理中添加以下权限：
   - `sheets:spreadsheet:create` - 创建电子表格
   - `sheets:spreadsheet` - 查看、编辑和管理电子表格
   - `drive:drive` - 查看、编辑、创建和删除云空间中的文件

### 2. 配置应用信息

修改 `cmd/main.go` 文件中的应用配置：

```go
var (
    appID     = "your_app_id"     // 替换为您的应用ID
    appSecret = "your_app_secret" // 替换为您的应用密钥
)
```

## 运行程序

```bash
# 进入项目目录
cd cmd

# 运行程序
go run main.go
```

## 程序输出

程序成功运行后会输出类似以下信息：

```
电子表格创建成功，Token: shtcnmBA*****
数据写入完成！
电子表格创建成功！
```

## 创建的表格结构

程序会创建一个名为"库存管理表格"的电子表格，包含以下结构：

| 名称 | 数量 | 时间 |
|------|------|------|
| 苹果 | 100  | 2025-01-XX XX:XX:XX |
| 香蕉 | 50   | 2025-01-XX XX:XX:XX |
| 橙子 | 75   | 2025-01-XX XX:XX:XX |
| 葡萄 | 30   | 2025-01-XX XX:XX:XX |
| 西瓜 | 20   | 2025-01-XX XX:XX:XX |

## 代码说明

### 主要函数

1. **main()** - 程序入口，创建 Lark client 并调用创建表格函数
2. **createSpreadsheetWithData()** - 创建电子表格并写入数据
3. **writeDataToSheet()** - 向指定范围写入数据

### 关键 API 调用

- `client.Sheets.Spreadsheet.Create()` - 创建电子表格
- `client.Sheets.SpreadsheetSheetValuesBatch.Update()` - 批量更新单元格数据

## 自定义修改

### 修改表格标题

```go
Title("您的表格标题").
```

### 修改数据内容

修改 `data` 变量中的内容：

```go
data := [][]interface{}{
    {"商品名称", 数量, "时间字符串"},
    // 添加更多数据行...
}
```

### 修改表头

修改 `headers` 变量：

```go
headers := [][]interface{}{
    {"列1", "列2", "列3", "列4"}, // 可以添加更多列
}
```

## 注意事项

1. 确保应用有足够的权限访问飞书电子表格 API
2. 网络连接正常，能够访问飞书开放平台 API
3. App ID 和 App Secret 配置正确
4. 建议在测试环境先验证功能

## 错误处理

程序包含完整的错误处理机制：

- API 调用失败会返回详细错误信息
- 网络连接问题会被捕获并报告
- 权限不足等问题会有明确提示

## 依赖包

- `github.com/larksuite/oapi-sdk-go/v3` - 飞书开放平台 Go SDK

## 许可证

本项目遵循项目根目录的许可证。
