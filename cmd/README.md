# 飞书电子表格创建工具

这个程序使用飞书 Lark SDK 创建一个包含名称、数量、时间等字段的电子表格。

## 功能特性

- 使用飞书开放平台 API 创建电子表格
- 自动添加表头（名称、数量、时间）
- 插入示例数据
- 支持批量数据更新
- 完整的错误处理

## 使用前准备

### 1. 创建飞书应用

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 登录并创建企业自建应用
3. 获取应用的 `App ID` 和 `App Secret`
4. 在应用权限管理中添加以下权限：
   - `sheets:spreadsheet:create` - 创建电子表格
   - `sheets:spreadsheet` - 查看、编辑和管理电子表格
   - `drive:drive` - 查看、编辑、创建和删除云空间中的文件

### 2. 配置应用信息

修改 `cmd/main.go` 文件中的应用配置：

```go
var (
    appID     = "your_app_id"     // 替换为您的应用ID
    appSecret = "your_app_secret" // 替换为您的应用密钥
)
```

## 运行程序

### 基础版本

```bash
# 进入项目目录
cd cmd

# 运行基础版本（需要先修改代码中的 appID 和 appSecret）
go run main.go
```

### 高级版本（推荐）

```bash
# 进入项目目录
cd cmd

# 运行高级版本（支持命令行参数）
go run advanced_example.go -app-id=你的应用ID -app-secret=你的应用密钥

# 自定义表格标题
go run advanced_example.go -app-id=你的应用ID -app-secret=你的应用密钥 -title="我的库存管理表"

# 查看帮助信息
go run advanced_example.go -help
```

### 编译后运行

```bash
# 编译程序
go build -o sheet-creator main.go
go build -o advanced-sheet-creator advanced_example.go

# 运行编译后的程序
./advanced-sheet-creator -app-id=你的应用ID -app-secret=你的应用密钥
```

## 程序输出

程序成功运行后会输出类似以下信息：

```
电子表格创建成功，Token: shtcnmBA*****
数据写入完成！
电子表格创建成功！
```

## 创建的表格结构

程序会创建一个名为"库存管理表格"的电子表格，包含以下结构：

| 名称 | 数量 | 时间 | 单价 | 总价 |
|------|------|------|------|------|
| 苹果 | 100  | 2025-01-XX XX:XX:XX | 5.5 | 550.0 |
| 香蕉 | 50   | 2025-01-XX XX:XX:XX | 3.2 | 160.0 |
| 橙子 | 75   | 2025-01-XX XX:XX:XX | 4.8 | 360.0 |
| 葡萄 | 30   | 2025-01-XX XX:XX:XX | 12.0 | 360.0 |
| 西瓜 | 20   | 2025-01-XX XX:XX:XX | 15.5 | 310.0 |
| 草莓 | 80   | 2025-01-XX XX:XX:XX | 8.0 | 640.0 |
| 蓝莓 | 25   | 2025-01-XX XX:XX:XX | 20.0 | 500.0 |

### 功能特点

- **自动计算总价**: 程序会自动计算每个商品的总价（数量 × 单价）
- **时间戳记录**: 每个商品都有对应的时间戳
- **数据统计**: 高级版本会显示数据统计信息（商品种类、总数量、总价值等）
- **灵活配置**: 支持自定义表格标题和数据内容

## 文件说明

### 主要文件

1. **main.go** - 基础版本，功能简单，适合学习和理解基本流程
2. **advanced_example.go** - 高级版本，支持命令行参数，功能更完善
3. **config.example.go** - 配置示例文件，包含权限说明和示例数据结构
4. **README.md** - 详细的使用说明文档

### 代码说明

#### 基础版本 (main.go)

**主要函数:**
1. **main()** - 程序入口，创建 Lark client 并调用创建表格函数
2. **createSpreadsheetWithData()** - 创建电子表格并写入数据
3. **writeDataToSheetNative()** - 使用原生 API 向指定范围写入数据

#### 高级版本 (advanced_example.go)

**主要函数:**
1. **main()** - 处理命令行参数，创建 client
2. **createAdvancedSpreadsheet()** - 高级电子表格创建流程
3. **createSpreadsheet()** - 专门的电子表格创建函数
4. **generateSampleData()** - 生成示例数据
5. **convertToTableData()** - 将数据转换为表格格式
6. **writeDataToSheet()** - 写入数据到表格
7. **showDataStatistics()** - 显示数据统计信息

### 关键 API 调用

- **创建电子表格**: `POST /open-apis/sheets/v3/spreadsheets`
- **写入数据**: `PUT /open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/values`

## 自定义修改

### 修改表格标题

```go
Title("您的表格标题").
```

### 修改数据内容

修改 `data` 变量中的内容：

```go
data := [][]interface{}{
    {"商品名称", 数量, "时间字符串"},
    // 添加更多数据行...
}
```

### 修改表头

修改 `headers` 变量：

```go
headers := [][]interface{}{
    {"列1", "列2", "列3", "列4"}, // 可以添加更多列
}
```

## 注意事项

1. 确保应用有足够的权限访问飞书电子表格 API
2. 网络连接正常，能够访问飞书开放平台 API
3. App ID 和 App Secret 配置正确
4. 建议在测试环境先验证功能

## 错误处理

程序包含完整的错误处理机制：

- API 调用失败会返回详细错误信息
- 网络连接问题会被捕获并报告
- 权限不足等问题会有明确提示

## 依赖包

- `github.com/larksuite/oapi-sdk-go/v3` - 飞书开放平台 Go SDK

## 许可证

本项目遵循项目根目录的许可证。
