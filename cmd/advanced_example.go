package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
)

// 命令行参数
var (
	appIDFlag     = flag.String("app-id", "cli_a8e3c55af7f81028", "飞书应用ID")
	appSecretFlag = flag.String("app-secret", "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC", "飞书应用密钥")
	titleFlag     = flag.String("title", "库存管理表格", "电子表格标题")
	helpFlag      = flag.Bool("help", false, "显示帮助信息")
)

// 数据结构定义
type ValueRange struct {
	MajorDimension string        `json:"majorDimension"`
	Range          string        `json:"range"`
	Revision       int           `json:"revision"`
	Values         []interface{} `json:"values"`
}

type SpreadsheetRespData struct {
	ValueRange *ValueRange `json:"valueRange"`
}

type SpreadsheetResp struct {
	*larkcore.ApiResp
	larkcore.CodeError
	Data *SpreadsheetRespData `json:"data"`
}

type Spreadsheet struct {
	SpreadsheetToken string `json:"spreadsheet_token"`
	Title            string `json:"title"`
	FolderToken      string `json:"folder_token"`
	URL              string `json:"url"`
}

type CreateSpreadsheetRespData struct {
	Spreadsheet *Spreadsheet `json:"spreadsheet"`
}

type CreateSpreadsheetResp struct {
	*larkcore.ApiResp
	larkcore.CodeError
	Data *CreateSpreadsheetRespData `json:"data"`
}

// 商品数据结构
type Product struct {
	Name     string
	Quantity int
	Price    float64
	Time     string
}

func main() {
	flag.Parse()

	if *helpFlag {
		showHelp()
		return
	}

	// 检查必需参数
	if *appIDFlag == "" || *appSecretFlag == "" {
		fmt.Println("错误: 必须提供 app-id 和 app-secret 参数")
		fmt.Println("使用 -help 查看帮助信息")
		os.Exit(1)
	}

	// 创建 Lark client
	client := lark.NewClient(*appIDFlag, *appSecretFlag,
		lark.WithLogLevel(larkcore.LogLevelInfo),
		lark.WithReqTimeout(30*time.Second),
		lark.WithEnableTokenCache(true),
		lark.WithHttpClient(http.DefaultClient))

	// 创建包含名称、数量、时间字段的电子表格
	err := createAdvancedSpreadsheet(client, *titleFlag)
	if err != nil {
		log.Fatalf("创建电子表格失败: %v", err)
	}

	fmt.Println("电子表格创建成功！")
}

func showHelp() {
	fmt.Println("飞书电子表格创建工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run advanced_example.go -app-id=<应用ID> -app-secret=<应用密钥> [选项]")
	fmt.Println()
	fmt.Println("必需参数:")
	fmt.Println("  -app-id string      飞书应用ID")
	fmt.Println("  -app-secret string  飞书应用密钥")
	fmt.Println()
	fmt.Println("可选参数:")
	fmt.Println("  -title string       电子表格标题 (默认: \"库存管理表格\")")
	fmt.Println("  -help              显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run advanced_example.go -app-id=cli_xxx -app-secret=xxx -title=\"我的库存表\"")
	fmt.Println()
	fmt.Println("注意:")
	fmt.Println("  1. 请确保您的飞书应用有创建电子表格的权限")
	fmt.Println("  2. 需要以下权限: sheets:spreadsheet:create, sheets:spreadsheet")
}

func createAdvancedSpreadsheet(client *lark.Client, title string) error {
	fmt.Printf("正在创建电子表格: %s\n", title)

	// 1. 创建电子表格
	spreadsheetToken, err := createSpreadsheet(client, title)
	if err != nil {
		return fmt.Errorf("创建电子表格失败: %w", err)
	}

	fmt.Printf("电子表格创建成功，Token: %s\n", spreadsheetToken)
	fmt.Printf("电子表格URL: https://feishu.cn/sheets/%s\n", spreadsheetToken)

	// 2. 准备示例数据
	products := generateSampleData()

	// 3. 转换为表格数据
	tableData := convertToTableData(products)

	// 4. 写入数据
	fmt.Println("正在写入数据...")
	err = writeDataToSheet(client, spreadsheetToken, "A1:E8", tableData)
	if err != nil {
		return fmt.Errorf("写入数据失败: %w", err)
	}

	fmt.Println("数据写入完成！")
	fmt.Printf("请访问飞书查看您的电子表格: https://feishu.cn/sheets/%s\n", spreadsheetToken)

	// 5. 显示数据统计
	showDataStatistics(products)

	return nil
}

func createSpreadsheet(client *lark.Client, title string) (string, error) {
	ctx := context.Background()

	createBody := map[string]interface{}{
		"title":        title,
		"folder_token": "",
	}

	createReq := &larkcore.ApiReq{
		HttpMethod:                http.MethodPost,
		ApiPath:                   "/open-apis/sheets/v3/spreadsheets",
		Body:                      createBody,
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant},
	}

	createResp, err := client.Do(ctx, createReq)
	if err != nil {
		return "", err
	}

	createResult := &CreateSpreadsheetResp{}
	err = json.Unmarshal(createResp.RawBody, createResult)
	if err != nil {
		return "", fmt.Errorf("解析创建响应失败: %w", err)
	}

	createResult.ApiResp = createResp
	if createResult.Code != 0 {
		return "", fmt.Errorf("创建电子表格失败，错误码: %d, 错误信息: %s, 请求ID: %s",
			createResult.Code, createResult.Msg, createResult.RequestId())
	}

	return createResult.Data.Spreadsheet.SpreadsheetToken, nil
}

func generateSampleData() []Product {
	currentTime := time.Now()
	return []Product{
		{"苹果", 100, 5.5, currentTime.Format("2006-01-02 15:04:05")},
		{"香蕉", 50, 3.2, currentTime.Add(-1 * time.Hour).Format("2006-01-02 15:04:05")},
		{"橙子", 75, 4.8, currentTime.Add(-2 * time.Hour).Format("2006-01-02 15:04:05")},
		{"葡萄", 30, 12.0, currentTime.Add(-3 * time.Hour).Format("2006-01-02 15:04:05")},
		{"西瓜", 20, 15.5, currentTime.Add(-4 * time.Hour).Format("2006-01-02 15:04:05")},
		{"草莓", 80, 8.0, currentTime.Add(-5 * time.Hour).Format("2006-01-02 15:04:05")},
		{"蓝莓", 25, 20.0, currentTime.Add(-6 * time.Hour).Format("2006-01-02 15:04:05")},
	}
}

func convertToTableData(products []Product) [][]interface{} {
	data := [][]interface{}{
		{"名称", "数量", "时间", "单价", "总价"}, // 表头
	}

	for _, product := range products {
		total := float64(product.Quantity) * product.Price
		row := []interface{}{
			product.Name,
			product.Quantity,
			product.Time,
			product.Price,
			total,
		}
		data = append(data, row)
	}

	return data
}

func writeDataToSheet(client *lark.Client, spreadsheetToken, cellRange string, data [][]interface{}) error {
	ctx := context.Background()

	valueRange := map[string]interface{}{
		"range":  cellRange,
		"values": data,
	}
	body := map[string]interface{}{
		"valueRange": valueRange,
	}

	req := &larkcore.ApiReq{
		HttpMethod:                http.MethodPut,
		ApiPath:                   fmt.Sprintf("/open-apis/sheets/v2/spreadsheets/%s/values", spreadsheetToken),
		Body:                      body,
		SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant},
	}

	resp, err := client.Do(ctx, req)
	if err != nil {
		return err
	}

	spreadsheetResp := &SpreadsheetResp{}
	err = json.Unmarshal(resp.RawBody, spreadsheetResp)
	if err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	spreadsheetResp.ApiResp = resp
	if spreadsheetResp.Code != 0 {
		return fmt.Errorf("写入数据失败，错误码: %d, 错误信息: %s, 请求ID: %s",
			spreadsheetResp.Code, spreadsheetResp.Msg, spreadsheetResp.RequestId())
	}

	fmt.Printf("成功写入数据到范围: %s\n", cellRange)
	return nil
}

func showDataStatistics(products []Product) {
	fmt.Println("\n=== 数据统计 ===")

	totalItems := 0
	totalValue := 0.0

	for _, product := range products {
		totalItems += product.Quantity
		totalValue += float64(product.Quantity) * product.Price
	}

	fmt.Printf("商品种类: %d\n", len(products))
	fmt.Printf("总数量: %d\n", totalItems)
	fmt.Printf("总价值: %.2f 元\n", totalValue)
	fmt.Printf("平均单价: %.2f 元\n", totalValue/float64(totalItems))
}
