package main

import (
	"fmt"
	"reflect"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	_ "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
)

func main() {
	fmt.Println("Testing imports...")
	client := lark.NewClient("test", "test")
	fmt.Printf("Client type: %T\n", client)
	fmt.Printf("Sheets service: %T\n", client.Sheets)
	fmt.Printf("Sheets V3 service: %T\n", client.Sheets.V3)

	// 查看 SpreadsheetSheet 的方法
	spreadsheetSheetType := reflect.TypeOf(client.Sheets.V3.SpreadsheetSheet)
	fmt.Printf("SpreadsheetSheet type: %s\n", spreadsheetSheetType)
	fmt.Printf("SpreadsheetSheet has %d methods:\n", spreadsheetSheetType.NumMethod())
	for i := 0; i < spreadsheetSheetType.NumMethod(); i++ {
		method := spreadsheetSheetType.Method(i)
		fmt.Printf("  %s\n", method.Name)
	}
}
