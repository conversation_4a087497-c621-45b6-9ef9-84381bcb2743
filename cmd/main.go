package main

import (
	"context"
	"fmt"
	"log"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	"net/http"
)

var (
	appID     = "your_app_id"     // 请替换为您的应用ID
	appSecret = "your_app_secret" // 请替换为您的应用密钥
)

func main() {
	// 创建 Lark client
	client := lark.NewClient(appID, appSecret,
		lark.WithLogLevel(larkcore.LogLevelDebug),
		lark.WithReqTimeout(30*time.Second),
		lark.WithEnableTokenCache(true),
		lark.WithHttpClient(http.DefaultClient))

	// 创建包含名称、数量、时间字段的电子表格
	err := createSpreadsheetWithData(client)
	if err != nil {
		log.Fatalf("创建电子表格失败: %v", err)
	}

	fmt.Println("电子表格创建成功！")
}

// createSpreadsheetWithData 创建包含名称、数量、时间字段的电子表格
func createSpreadsheetWithData(client *lark.Client) error {
	ctx := context.Background()

	// 1. 创建电子表格
	createReq := larksheets.NewCreateSpreadsheetReqBuilder().
		Body(larksheets.NewCreateSpreadsheetReqBodyBuilder().
			Title("库存管理表格").
			FolderToken(""). // 可以指定文件夹，留空则创建在根目录
			Build()).
		Build()

	createResp, err := client.Sheets.Spreadsheet.Create(ctx, createReq)
	if err != nil {
		return fmt.Errorf("创建电子表格失败: %w", err)
	}

	if !createResp.Success() {
		return fmt.Errorf("创建电子表格API调用失败: %s", createResp.Msg)
	}

	spreadsheetToken := createResp.Data.Spreadsheet.SpreadsheetToken
	fmt.Printf("电子表格创建成功，Token: %s\n", *spreadsheetToken)

	// 2. 准备表头和数据
	headers := [][]interface{}{
		{"名称", "数量", "时间"},
	}

	// 示例数据
	data := [][]interface{}{
		{"苹果", 100, time.Now().Format("2006-01-02 15:04:05")},
		{"香蕉", 50, time.Now().Add(-1 * time.Hour).Format("2006-01-02 15:04:05")},
		{"橙子", 75, time.Now().Add(-2 * time.Hour).Format("2006-01-02 15:04:05")},
		{"葡萄", 30, time.Now().Add(-3 * time.Hour).Format("2006-01-02 15:04:05")},
		{"西瓜", 20, time.Now().Add(-4 * time.Hour).Format("2006-01-02 15:04:05")},
	}

	// 3. 写入表头
	err = writeDataToSheet(client, *spreadsheetToken, "A1:C1", headers)
	if err != nil {
		return fmt.Errorf("写入表头失败: %w", err)
	}

	// 4. 写入数据
	err = writeDataToSheet(client, *spreadsheetToken, "A2:C6", data)
	if err != nil {
		return fmt.Errorf("写入数据失败: %w", err)
	}

	fmt.Println("数据写入完成！")
	return nil
}

// writeDataToSheet 向指定的电子表格写入数据
func writeDataToSheet(client *lark.Client, spreadsheetToken, cellRange string, data [][]interface{}) error {
	ctx := context.Background()

	// 构建批量更新请求
	updateReq := larksheets.NewBatchUpdateSpreadsheetSheetValueReqBuilder().
		SpreadsheetToken(spreadsheetToken).
		Body(larksheets.NewBatchUpdateSpreadsheetSheetValueReqBodyBuilder().
			ValueRanges([]*larksheets.ValueRange{
				larksheets.NewValueRangeBuilder().
					Range(cellRange).
					Values(data).
					Build(),
			}).
			Build()).
		Build()

	updateResp, err := client.Sheets.SpreadsheetSheetValuesBatch.Update(ctx, updateReq)
	if err != nil {
		return fmt.Errorf("批量更新数据失败: %w", err)
	}

	if !updateResp.Success() {
		return fmt.Errorf("批量更新数据API调用失败: %s", updateResp.Msg)
	}

	return nil
}
