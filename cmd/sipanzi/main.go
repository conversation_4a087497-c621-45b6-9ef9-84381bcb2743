package main

import (
	"context"
	"flag"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"os"

	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"gitlab.jcwork.net/assets-management/sipanzi/server/health"
	"go.uber.org/zap"
)

func main() {

	env := os.Getenv("ENV")
	if env == "" {
		env = consts.EnvLocal
	}

	log.InitLogger(env)

	log.Logger.Info("cedefi-sipanzi started", zap.String("ENV", env))

	confFlag := flag.String("conf", "config.yaml", "configuration file path")
	flag.Parse()

	cfg, err := config.LoadConfig(env, *confFlag)
	if err != nil {
		panic(err)
	}
	ctx := context.Background()

	db.InitDb(cfg.Db)
	db.InitRdb(cfg.Rdb, env)
	db.InitPublisher(cfg.Kafka)
	health.NewServer(cfg.Server.Health).Serve()
	common.StartSwapStoreLoop(ctx) // batch insert service

	service.InitSolPriceWatcher(cfg.Solana) // make sure sol price is ready
	common.TimedPrintCounter()
	block.NewBlockWatcher(cfg.Solana).Start()
	job.NewJobManager(cfg).Start()

	select {}
}
