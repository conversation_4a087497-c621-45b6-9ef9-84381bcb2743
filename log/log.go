package log

import (
	"context"
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc"

	"gitlab.jcwork.net/assets-management/sipanzi/consts"
)

var Logger *zap.Logger

func InitLogger(env string) {

	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
	encoderConfig.TimeKey = "time"
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	var logLevel zapcore.Level
	switch env {
	case consts.EnvLocal:
		logLevel = zap.DebugLevel
	case consts.EnvDev:
		logLevel = zap.DebugLevel
	case consts.EnvTest:
		logLevel = zap.DebugLevel
	case consts.EnvProd:
		logLevel = zap.InfoLevel
	default:
		logLevel = zap.InfoLevel
	}
	core := zapcore.NewCore(encoder, os.Stdout, logLevel)

	Logger = zap.New(core, zap.AddCaller())
}

func LoggingInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	resp, err = handler(ctx, req)
	Logger.Info("[gRPC]", zap.String("method", info.FullMethod), zap.Any("req", req), zap.Any("resp", resp))
	return resp, err
}
