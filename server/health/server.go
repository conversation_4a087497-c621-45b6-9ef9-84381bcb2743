package health

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"go.uber.org/zap"
)

type Server struct {
	Port int
}

func NewServer(cfg config.HealthConfig) Server {
	return Server{
		Port: cfg.Port,
	}
}

func (s Server) Serve() {
	log.Logger.Info("run health server", zap.Int("port", s.Port))

	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	health := router.Group("/health")
	{
		health.GET("/liveness", func(c *gin.Context) {
			c.J<PERSON>N(200, gin.H{"status": "alive"})
		})
		health.GET("/readiness", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ready"})
		})
	}

	go func() {
		if err := router.Run(fmt.Sprintf(":%d", s.Port)); err != nil {
			panic(err)
		}
	}()

	log.Logger.Info(fmt.Sprintf("health server started on port %d", s.Port))
}
